import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AntdProvider } from '@/components/providers/AntdProvider'
import { SessionProvider } from '@/components/providers/SessionProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '肺功能数据管理平台',
  description: '肺功能数据管理平台 - 专业的医疗数据管理系统',
  keywords: '肺功能, 数据管理, 医疗, 健康管理',
  authors: [{ name: '肺功能数据管理平台' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <SessionProvider>
          <AntdProvider>{children}</AntdProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
