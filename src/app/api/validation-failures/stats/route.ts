import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/validation-failures/stats - 获取验证失败统计信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 403 })
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const lastWeek = new Date(today)
    lastWeek.setDate(lastWeek.getDate() - 7)
    const lastMonth = new Date(today)
    lastMonth.setDate(lastMonth.getDate() - 30)

    // 基础统计
    const [
      totalCount,
      todayCount,
      pendingCount,
      resolvedCount,
      errorTypeStats,
      formIdStats,
      recentTrends,
    ] = await Promise.all([
      // 总数
      prisma.webhookValidationFailure.count(),

      // 今日新增
      prisma.webhookValidationFailure.count({
        where: { createdAt: { gte: today } },
      }),

      // 待处理数量
      prisma.webhookValidationFailure.count({
        where: { status: 'pending' },
      }),

      // 已解决数量
      prisma.webhookValidationFailure.count({
        where: { status: 'resolved' },
      }),

      // 错误类型统计
      prisma.webhookValidationFailure.groupBy({
        by: ['errorType'],
        _count: true,
        orderBy: { _count: { errorType: 'desc' } },
      }),

      // 表单ID统计
      prisma.webhookValidationFailure.groupBy({
        by: ['formId'],
        _count: true,
        orderBy: { _count: { formId: 'desc' } },
        take: 10,
      }),

      // 最近趋势（过去7天每天的数量）
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM webhook_validation_failures 
        WHERE created_at >= ${lastWeek}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `,
    ])

    // 状态分布统计
    const statusStats = await prisma.webhookValidationFailure.groupBy({
      by: ['status'],
      _count: true,
    })

    // 处理时间统计
    const resolutionTimeStats = (await prisma.$queryRaw`
      SELECT 
        AVG(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as avg_hours,
        MIN(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as min_hours,
        MAX(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as max_hours
      FROM webhook_validation_failures 
      WHERE status = 'resolved' AND resolved_at IS NOT NULL
    `) as Array<{
      avg_hours: number | null
      min_hours: number | null
      max_hours: number | null
    }>

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          total: totalCount,
          today: todayCount,
          pending: pendingCount,
          resolved: resolvedCount,
          resolution_rate:
            totalCount > 0
              ? ((resolvedCount / totalCount) * 100).toFixed(1)
              : '0.0',
        },
        errorTypeStats: errorTypeStats.map(stat => ({
          errorType: stat.errorType,
          count: stat._count,
          percentage:
            totalCount > 0
              ? ((stat._count / totalCount) * 100).toFixed(1)
              : '0.0',
        })),
        formIdStats: formIdStats.map(stat => ({
          formId: stat.formId,
          count: stat._count,
          percentage:
            totalCount > 0
              ? ((stat._count / totalCount) * 100).toFixed(1)
              : '0.0',
        })),
        statusStats: statusStats.map(stat => ({
          status: stat.status,
          count: stat._count,
          percentage:
            totalCount > 0
              ? ((stat._count / totalCount) * 100).toFixed(1)
              : '0.0',
        })),
        recentTrends: recentTrends,
        resolutionTime: resolutionTimeStats[0] || {
          avg_hours: null,
          min_hours: null,
          max_hours: null,
        },
      },
    })
  } catch (error) {
    console.error('获取验证失败统计信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取验证失败统计信息失败' },
      { status: 500 }
    )
  }
}
