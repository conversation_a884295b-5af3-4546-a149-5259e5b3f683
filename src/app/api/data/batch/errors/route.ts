import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'

// GET /api/data/batch/errors - 下载错误报告
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const operationId = searchParams.get('operationId')

    if (!operationId) {
      return NextResponse.json(
        { success: false, error: '缺少操作ID' },
        { status: 400 }
      )
    }

    // 查找对应的系统日志
    const log = await prisma.systemLog.findFirst({
      where: {
        id: parseInt(operationId),
        action: 'BATCH_IMPORT_DATA',
        userId: session.user.id,
      },
    })

    if (!log) {
      return NextResponse.json(
        { success: false, error: '找不到对应的操作记录' },
        { status: 404 }
      )
    }

    const details = log.details as any
    if (!details || !details.errorSample) {
      return NextResponse.json(
        { success: false, error: '该操作没有错误记录' },
        { status: 400 }
      )
    }

    // 获取表单配置用于字段映射
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId: details.formId, isActive: true },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    const fieldMapping = formConfig.fieldMapping as Record<string, any>

    // 构建错误报告数据
    const errorReportData = []

    // 添加汇总信息
    const summaryData = [
      { 项目: '操作类型', 值: '批量导入数据' },
      { 项目: '表单名称', 值: formConfig.formName },
      { 项目: '文件名称', 值: details.fileName || '未知' },
      { 项目: '导入时间', 值: log.createdAt.toLocaleString('zh-CN') },
      { 项目: '总记录数', 值: details.totalRecords || 0 },
      { 项目: '成功记录数', 值: details.successRecords || 0 },
      { 项目: '失败记录数', 值: details.failedRecords || 0 },
      { 项目: '', 值: '' }, // 空行分隔
    ]

    // 添加错误详情标题
    summaryData.push({ 项目: '错误详情', 值: '' })
    summaryData.push({ 项目: '行号', 值: '错误信息' })

    errorReportData.push(...summaryData)

    // 添加具体错误记录
    if (details.errorSample && Array.isArray(details.errorSample)) {
      details.errorSample.forEach((errorItem: any) => {
        errorReportData.push({
          项目: `第${errorItem.row}行`,
          值: errorItem.error,
        })

        // 添加原始数据
        if (errorItem.data) {
          Object.entries(errorItem.data).forEach(([key, value]) => {
            errorReportData.push({
              项目: `  - ${key}`,
              值: value,
            })
          })
        }

        // 添加空行分隔
        errorReportData.push({ 项目: '', 值: '' })
      })
    }

    // 创建详细错误工作表
    const detailedErrorData: any[] = []
    if (details.errorSample && Array.isArray(details.errorSample)) {
      details.errorSample.forEach((errorItem: any) => {
        const errorRecord: any = {
          行号: errorItem.row,
          错误信息: errorItem.error,
          序号: errorItem.data?.['序号'] || '',
        }

        // 添加所有字段的原始数据
        Object.entries(fieldMapping).forEach(([key, config]) => {
          const fieldName = (config as any).name
          errorRecord[fieldName] = errorItem.data?.[fieldName] || ''
        })

        detailedErrorData.push(errorRecord)
      })
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 添加汇总工作表
    const summarySheet = XLSX.utils.json_to_sheet(errorReportData)
    summarySheet['!cols'] = [{ wch: 20 }, { wch: 50 }]
    XLSX.utils.book_append_sheet(workbook, summarySheet, '错误汇总')

    // 添加详细错误工作表
    if (detailedErrorData.length > 0) {
      const detailSheet = XLSX.utils.json_to_sheet(detailedErrorData)

      // 设置列宽
      const colWidths = Object.keys(detailedErrorData[0]).map(key => ({
        wch: Math.max(key.length + 2, 15),
      }))
      detailSheet['!cols'] = colWidths

      XLSX.utils.book_append_sheet(workbook, detailSheet, '错误详情')
    }

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    })

    // 记录下载日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'DOWNLOAD_ERROR_REPORT',
        resource: 'ErrorReport',
        resourceId: operationId,
        details: {
          originalOperationId: operationId,
          formId: details.formId,
          errorCount: details.errorSample?.length || 0,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="导入错误报告_${formConfig.formName}_${new Date().toISOString().split('T')[0]}.xlsx"`,
      },
    })
  } catch (error) {
    console.error('生成错误报告失败:', error)
    return NextResponse.json(
      { success: false, error: '生成错误报告失败' },
      { status: 500 }
    )
  }
}
