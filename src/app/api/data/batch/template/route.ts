import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'

// GET /api/data/batch/template - 下载导入模板
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const formId = searchParams.get('formId')

    if (!formId) {
      return NextResponse.json(
        { success: false, error: '缺少表单ID' },
        { status: 400 }
      )
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已禁用' },
        { status: 404 }
      )
    }

    const fieldMapping = formConfig.fieldMapping as Record<string, any>

    // 构建模板数据
    const templateData = []
    const headers: any = { 序号: '' }

    // 添加所有字段的中文名称作为列标题
    Object.entries(fieldMapping).forEach(([key, config]) => {
      headers[(config as any).name] = ''
    })

    // 添加示例数据行
    const exampleRow: any = { 序号: 1 }
    Object.entries(fieldMapping).forEach(([key, config]) => {
      const fieldConfig = config as any
      let exampleValue = ''

      switch (fieldConfig.type) {
        case 'number':
          exampleValue = '123'
          break
        case 'date':
          exampleValue = '2024-01-01'
          break
        case 'select':
          if (fieldConfig.options && fieldConfig.options.length > 0) {
            exampleValue = fieldConfig.options[0].label
          } else {
            exampleValue = '选项1'
          }
          break
        default:
          exampleValue = `示例${fieldConfig.name}`
      }

      exampleRow[fieldConfig.name] = exampleValue
    })

    templateData.push(headers, exampleRow)

    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.json_to_sheet(templateData, {
      skipHeader: false,
    })

    // 设置列宽
    const colWidths = Object.keys(headers).map(key => ({
      wch: Math.max(key.length + 2, 15),
    }))
    worksheet['!cols'] = colWidths

    // 添加数据验证和注释
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:Z100')

    // 为标题行设置样式（如果支持的话）
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
      if (worksheet[cellAddress]) {
        // 可以在这里设置样式，但xlsx库的免费版本有限制
      }
    }

    // 添加说明工作表
    const instructionData = [
      { 字段: '说明', 要求: '导入说明' },
      { 字段: '序号', 要求: '必填，数字类型，用于标识记录' },
    ]

    Object.entries(fieldMapping).forEach(([key, config]) => {
      const fieldConfig = config as any
      let requirement = fieldConfig.required ? '必填' : '可选'

      if (fieldConfig.type === 'number') {
        requirement += '，数字类型'
      } else if (fieldConfig.type === 'date') {
        requirement += '，日期格式：YYYY-MM-DD'
      } else if (fieldConfig.type === 'select' && fieldConfig.options) {
        requirement += `，可选值：${fieldConfig.options.map((opt: any) => opt.label).join('、')}`
      }

      instructionData.push({
        字段: fieldConfig.name,
        要求: requirement,
      })
    })

    const instructionSheet = XLSX.utils.json_to_sheet(instructionData)
    instructionSheet['!cols'] = [{ wch: 20 }, { wch: 50 }]

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '数据模板')
    XLSX.utils.book_append_sheet(workbook, instructionSheet, '导入说明')

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    })

    // 记录下载日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'DOWNLOAD_TEMPLATE',
        resource: 'FormTemplate',
        resourceId: formId,
        details: {
          formId,
          formName: formConfig.formName,
          fieldCount: Object.keys(fieldMapping).length,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${formConfig.formName}_导入模板_${new Date().toISOString().split('T')[0]}.xlsx"`,
      },
    })
  } catch (error) {
    console.error('生成模板失败:', error)
    return NextResponse.json(
      { success: false, error: '生成模板失败' },
      { status: 500 }
    )
  }
}
